import HomeLayout from '../components/HomeLayout'
import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { UserGroupIcon, CakeIcon, HeartIcon, BoltIcon, AcademicCapIcon } from '@heroicons/react/24/solid'
import { FaPaw } from 'react-icons/fa'

function getBlogPosts(limit?: number) {
  const blogDir = path.join(process.cwd(), 'content/blog')
  let files: string[] = []
  try {
    files = fs.readdirSync(blogDir)
  } catch {
    files = []
  }
  const icons = [
    <FaPaw className="w-6 h-6 text-primary" key="paw" />,
    <UserGroupIcon className="w-6 h-6 text-primary" key="group" />,
    <CakeIcon className="w-6 h-6 text-primary" key="cake" />,
    <HeartIcon className="w-6 h-6 text-primary" key="heart" />,
  ]
  return files
    .filter((file) => file.endsWith('.md'))
    .map((file, i) => {
      const filePath = path.join(blogDir, file)
      const content = fs.readFileSync(filePath, 'utf-8')
      const { data } = matter(content)
      return {
        slug: file.replace(/.md$/, ''),
        title: data.title || 'Utan titel',
        date: data.date || '',
        excerpt: data.excerpt || '',
        image: data.image || '/images/blog-traning.jpg',
        icon: icons[i % icons.length],
      }
    })
    .sort((a, b) => (a.date < b.date ? 1 : -1))
    .slice(0, limit || 100)
}

export default function Page() {
  const posts = getBlogPosts(6)
  return (
    <HomeLayout>
      <section className="relative w-full min-h-[60vh] md:min-h-[80vh] flex items-center justify-center mb-10 animate-fade-in overflow-hidden">
        <video
          src="/video/hero.mp4"
          autoPlay
          loop
          muted
          playsInline
          className="absolute inset-0 w-full h-full object-cover object-center z-0"
          poster="/images/hero.jpg"
        />
        <div className="absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
          <div className="w-full max-w-3xl h-80 md:h-[28rem] mx-auto rounded-3xl bg-gradient-to-b from-white/90 via-white/70 to-white/0 shadow-2xl blur-sm"></div>
        </div>
        <div className="relative z-20 flex flex-col items-center justify-center w-full h-full">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-accent drop-shadow-lg">Labrador retriever info</h1>
          <div className="text-lg md:text-2xl mb-8 max-w-3xl text-center drop-shadow space-y-5">
            <p>Välkommen till Labrador retriever info – din trygga guide till livet med labrador!</p>
            <p>Här hittar du inspirerande artiklar, handfasta guider och glada tips för hela familjen. Oavsett om du drömmer om en valp, redan har en labbe, eller bara är nyfiken på rasen – du är hemma här.</p>
          </div>
        </div>
      </section>

      <h2 className="text-2xl font-semibold mt-10 mb-6">Senaste nytt från bloggen</h2>
      <div className="grid md:grid-cols-2 gap-8">
        {posts.map((post) => (
          <article key={post.slug} className="bg-white/70 rounded-xl shadow-lg p-4 flex flex-col md:flex-row gap-4 animate-fade-in border border-yellow-100 hover:shadow-xl transition-shadow">
            <img src={post.image} alt={post.title} className="w-full md:w-48 h-32 object-cover rounded-lg" />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">{post.icon}<h3 className="text-xl font-bold text-accent">{post.title}</h3></div>
              <p className="text-sm text-gray-600 mb-2">{post.date}</p>
              <p className="text-gray-800 text-base mb-2">{post.excerpt}</p>
              <a href={`/blog/${post.slug}`} className="text-primary underline font-semibold">Läs mer</a>
            </div>
          </article>
        ))}
      </div>
      {/* Sektion: Därför älskar vi Labradoren */}
      <section className="my-16 py-12 px-4 rounded-3xl bg-gradient-to-br from-yellow-100 via-white to-yellow-200 shadow-xl animate-fade-in">
        <h2 className="text-3xl font-bold text-accent mb-8 text-center">Därför älskar vi Labradoren</h2>
        <div className="flex flex-wrap justify-center gap-8">
          <div className="flex flex-col items-center max-w-xs">
            <FaPaw className="w-12 h-12 text-primary mb-2" />
            <span className="text-lg font-semibold">Barnvänlig & trygg</span>
            <span className="text-gray-700 text-center">Labradoren är en riktig familjehund – alltid glad, tålmodig och älskar att vara med barn.</span>
          </div>
          <div className="flex flex-col items-center max-w-xs">
            <AcademicCapIcon className="w-12 h-12 text-primary mb-2" />
            <span className="text-lg font-semibold">Intelligent & lättlärd</span>
            <span className="text-gray-700 text-center">En labbe vill samarbeta och lär sig snabbt – perfekt för både nybörjare och erfarna hundägare.</span>
          </div>
          <div className="flex flex-col items-center max-w-xs">
            <BoltIcon className="w-12 h-12 text-primary mb-2" />
            <span className="text-lg font-semibold">Aktiv & energisk</span>
            <span className="text-gray-700 text-center">Alltid redo för äventyr, lek och långa promenader – en trogen kompis i vardagen.</span>
          </div>
          <div className="flex flex-col items-center max-w-xs">
            <HeartIcon className="w-12 h-12 text-primary mb-2" />
            <span className="text-lg font-semibold">Lojal & kärleksfull</span>
            <span className="text-gray-700 text-center">En labrador finns där i både glada och tuffa stunder – vänskap på riktigt.</span>
          </div>
        </div>
      </section>

      {/* Sektion: Labrador retriever i bilder */}
      <section className="my-16 py-12 px-4 bg-white/70 rounded-3xl shadow-xl animate-fade-in">
        <h2 className="text-3xl font-bold text-accent mb-8 text-center">Labrador retriever i bilder</h2>
        <div className="flex flex-wrap justify-center gap-8">
          <img src="/images/hero.jpg" alt="Labrador retriever" className="rounded-2xl shadow-lg w-72 h-56 object-cover hover:scale-105 transition-transform duration-300" />
          <img src="/images/blog-traning.jpg" alt="Labrador tränar" className="rounded-2xl shadow-lg w-72 h-56 object-cover hover:scale-105 transition-transform duration-300" />
          <img src="/images/blog-valp.jpg" alt="Labradorvalp" className="rounded-2xl shadow-lg w-72 h-56 object-cover hover:scale-105 transition-transform duration-300" />
        </div>
      </section>

      {/* Sektion: Så tar du hand om din Labrador */}
      <section className="my-16 py-12 px-4 bg-gradient-to-br from-yellow-50 via-white to-yellow-100 rounded-3xl shadow-xl animate-fade-in">
        <h2 className="text-3xl font-bold text-accent mb-8 text-center">Så tar du hand om din Labrador</h2>
        <ul className="grid md:grid-cols-4 gap-8">
          <li className="flex flex-col items-center">
            <img src="/images/blog-foder.jpg" alt="Foder" className="w-20 h-20 object-cover rounded-full shadow mb-2" />
            <span className="font-semibold">Ge rätt foder</span>
            <span className="text-gray-700 text-center text-sm">Välj kvalitetsfoder och anpassa mängden efter hundens behov.</span>
          </li>
          <li className="flex flex-col items-center">
            <img src="/images/blog-traning.jpg" alt="Motion" className="w-20 h-20 object-cover rounded-full shadow mb-2" />
            <span className="font-semibold">Daglig motion</span>
            <span className="text-gray-700 text-center text-sm">Labradoren behöver rörelse och aktivering varje dag.</span>
          </li>
          <li className="flex flex-col items-center">
            <img src="/images/blog-valp.jpg" alt="Socialisering" className="w-20 h-20 object-cover rounded-full shadow mb-2" />
            <span className="font-semibold">Socialisera tidigt</span>
            <span className="text-gray-700 text-center text-sm">Låt din hund träffa människor och andra djur från början.</span>
          </li>
          <li className="flex flex-col items-center">
            <HeartIcon className="w-12 h-12 text-primary mb-2" />
            <span className="font-semibold">Ge mycket kärlek</span>
            <span className="text-gray-700 text-center text-sm">Labradoren trivs bäst med närhet och omtanke.</span>
          </li>
        </ul>
      </section>

      {/* Sektion: Citat från labradorägare */}
      <section className="my-16 py-12 px-4 bg-white/80 rounded-3xl shadow-xl animate-fade-in flex flex-col items-center">
        <h2 className="text-3xl font-bold text-accent mb-8 text-center">Vad säger labradorägare?</h2>
        <div className="max-w-2xl w-full flex flex-col gap-8">
          <blockquote className="bg-yellow-50 border-l-4 border-primary p-6 rounded-xl shadow flex flex-col relative animate-fade-in">
            <span className="text-lg italic mb-2">“Vår labrador är den bästa vän vi någonsin haft – alltid glad, tålmodig och kärleksfull!”</span>
            <span className="text-sm font-semibold text-accent">– Anna, labradorägare</span>
          </blockquote>
          <blockquote className="bg-yellow-100 border-l-4 border-accent p-6 rounded-xl shadow flex flex-col relative animate-fade-in">
            <span className="text-lg italic mb-2">“Jag älskar hur lätt det är att träna vår labbe – och han älskar verkligen barnen!”</span>
            <span className="text-sm font-semibold text-accent">– Johan, småbarnspappa</span>
          </blockquote>
        </div>
      </section>
    </HomeLayout>
  )
}
