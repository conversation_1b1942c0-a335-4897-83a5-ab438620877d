# Labrador Retriever Info

En modern, SEO-optimerad hemsida och blogg om Labrador retrievers. Byggd med Next.js och Tailwind CSS.

## Funktioner
- **Responsiv design**
- **SEO-optimerade sidor**
- **Blogg med Markdown-stöd via GitHub**
- **GDPR & Cookiebanner**
- **Google Analytics-förberedd**

## Kom igång

1. **Installera beroenden**
   ```bash
   npm install
   ```
2. **Starta utvecklingsservern**
   ```bash
   npm run dev
   ```

## Lägga till blogginlägg
Lägg till en ny `.md`-fil i `content/blog/`-mappen. Inlägget visas automatiskt på bloggen och inkluderas automatiskt i sitemap.xml.

## Lägga till guider
Lägg till en ny `.md`-fil i `content/guides/`-mappen. Guiden visas automatiskt på guider-sidan och inkluderas automatiskt i sitemap.xml.

## Automatisk sitemap-generering
Webbplatsen genererar automatiskt en sitemap.xml som inkluderar:
- Alla statiska sidor (startsida, blogg, guider, kontakt, etc.)
- Alla blogginlägg från `content/blog/`
- Alla guider från `content/guides/`
- Korrekt prioritering och uppdateringsfrekvens
- Automatisk uppdatering när nytt innehåll läggs till

### Sitemap-kommandon
```bash
# Generera sitemap manuellt
npm run sitemap

# Sitemap genereras automatiskt vid build
npm run build
```

Sitemap är tillgänglig på: `https://labradorretrieverinfo.se/sitemap.xml`

## Byta ut bilder
Byt ut placeholder-bilder i `public/images/` mot dina egna när du har dem.

## Deployment
Projektet är förberett för enkel deployment på t.ex. Vercel eller Netlify.

---

**Kontakt:** <EMAIL>
