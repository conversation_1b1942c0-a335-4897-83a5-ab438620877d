# Labrador Retriever Info

En modern, SEO-optimerad hemsida och blogg om Labrador retrievers. Byggd med Next.js och Tailwind CSS.

## Funktioner
- **Responsiv design**
- **SEO-optimerade sidor**
- **Blogg med Markdown-stöd via GitHub**
- **GDPR & Cookiebanner**
- **Google Analytics-förberedd**

## Kom igång

1. **Installera beroenden**
   ```bash
   npm install
   ```
2. **Starta utvecklingsservern**
   ```bash
   npm run dev
   ```

## Lägga till blogginlägg
Lägg till en ny `.md`-fil i `content/blog/`-mappen. Inlägget visas automatiskt på bloggen.

## Byta ut bilder
Byt ut placeholder-bilder i `public/images/` mot dina egna när du har dem.

## Deployment
Projektet är förberett för enkel deployment på t.ex. Vercel eller Netlify.

---

**Kontakt:** <EMAIL>
