import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { remark } from 'remark'
import html from 'remark-html'
import HomeLayout from '../../../components/HomeLayout'

export async function generateStaticParams() {
  const guidesDir = path.join(process.cwd(), 'content/guides')
  let files: string[] = []
  try {
    files = fs.readdirSync(guidesDir)
  } catch {
    files = []
  }
  return files.filter((file) => file.endsWith('.md')).map((file) => ({ slug: file.replace(/.md$/, '') }))
}

export default async function GuidePage({ params }: { params: { slug: string } }) {
  const { slug } = params
  const filePath = path.join(process.cwd(), 'content/guides', `${slug}.md`)
  const fileContent = fs.readFileSync(filePath, 'utf-8')
  const { data, content } = matter(fileContent)
  const processedContent = await remark().use(html).process(content)
  const contentHtml = processedContent.toString()

  return (
    <HomeLayout>
      <article className="max-w-3xl mx-auto py-12 px-4 bg-white/90 rounded-2xl shadow-lg mt-8 animate-fade-in">
        <h1 className="text-3xl md:text-4xl font-bold mb-4 text-accent">{data.title}</h1>
        <div className="text-sm text-gray-500 mb-6">{data.date}</div>
        {data.image && (
          <img src={data.image} alt={data.title} className="rounded-xl shadow mb-8 w-full max-h-96 object-cover" />
        )}
        <div className="prose prose-lg max-w-none" dangerouslySetInnerHTML={{ __html: contentHtml }} />
      </article>
    </HomeLayout>
  )
}
