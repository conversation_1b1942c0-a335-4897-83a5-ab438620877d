import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'

export interface SitemapUrl {
  url: string
  lastModified: Date
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: number
}

export function getStaticPages(): SitemapUrl[] {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://labradorretrieverinfo.se'
  
  // Define static pages with their priorities and change frequencies
  const staticPages = [
    {
      path: '',
      priority: 1.0,
      changeFrequency: 'weekly' as const,
    },
    {
      path: '/blog',
      priority: 0.9,
      changeFrequency: 'daily' as const,
    },
    {
      path: '/guider',
      priority: 0.9,
      changeFrequency: 'weekly' as const,
    },
    {
      path: '/kontakt',
      priority: 0.7,
      changeFrequency: 'monthly' as const,
    },
    {
      path: '/gdpr/integritetspolicy',
      priority: 0.3,
      changeFrequency: 'yearly' as const,
    },
    {
      path: '/gdpr/cookiepolicy',
      priority: 0.3,
      changeFrequency: 'yearly' as const,
    },
  ]

  return staticPages.map(page => ({
    url: `${baseUrl}${page.path}`,
    lastModified: new Date(),
    changeFrequency: page.changeFrequency,
    priority: page.priority,
  }))
}

export function getBlogPosts(): SitemapUrl[] {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://labradorretrieverinfo.se'
  const blogDir = path.join(process.cwd(), 'content/blog')
  
  let files: string[] = []
  try {
    files = fs.readdirSync(blogDir)
  } catch {
    return []
  }

  return files
    .filter(file => file.endsWith('.md'))
    .map(file => {
      const filePath = path.join(blogDir, file)
      const slug = file.replace(/\.md$/, '')
      
      // Get file stats for lastModified
      const stats = fs.statSync(filePath)
      
      // Parse frontmatter for potential date override
      try {
        const content = fs.readFileSync(filePath, 'utf-8')
        const { data } = matter(content)
        
        // Use frontmatter date if available, otherwise file modification time
        const lastModified = data.date ? new Date(data.date) : stats.mtime
        
        return {
          url: `${baseUrl}/blog/${slug}`,
          lastModified,
          changeFrequency: 'monthly' as const,
          priority: 0.8,
        }
      } catch {
        return {
          url: `${baseUrl}/blog/${slug}`,
          lastModified: stats.mtime,
          changeFrequency: 'monthly' as const,
          priority: 0.8,
        }
      }
    })
    .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
}

export function getGuides(): SitemapUrl[] {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://labradorretrieverinfo.se'
  const guidesDir = path.join(process.cwd(), 'content/guides')
  
  let files: string[] = []
  try {
    files = fs.readdirSync(guidesDir)
  } catch {
    return []
  }

  return files
    .filter(file => file.endsWith('.md'))
    .map(file => {
      const filePath = path.join(guidesDir, file)
      const slug = file.replace(/\.md$/, '')
      
      // Get file stats for lastModified
      const stats = fs.statSync(filePath)
      
      // Parse frontmatter for potential date override
      try {
        const content = fs.readFileSync(filePath, 'utf-8')
        const { data } = matter(content)
        
        // Use frontmatter date if available, otherwise file modification time
        const lastModified = data.date ? new Date(data.date) : stats.mtime
        
        return {
          url: `${baseUrl}/guider/${slug}`,
          lastModified,
          changeFrequency: 'monthly' as const,
          priority: 0.8,
        }
      } catch {
        return {
          url: `${baseUrl}/guider/${slug}`,
          lastModified: stats.mtime,
          changeFrequency: 'monthly' as const,
          priority: 0.8,
        }
      }
    })
    .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
}

export function getAllSitemapUrls(): SitemapUrl[] {
  return [
    ...getStaticPages(),
    ...getBlogPosts(),
    ...getGuides(),
  ]
}

export function generateSitemapXml(urls: SitemapUrl[]): string {
  const urlEntries = urls.map(({ url, lastModified, changeFrequency, priority }) => `
  <url>
    <loc>${url}</loc>
    <lastmod>${lastModified.toISOString()}</lastmod>
    <changefreq>${changeFrequency}</changefreq>
    <priority>${priority}</priority>
  </url>`).join('')

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlEntries}
</urlset>`
}
