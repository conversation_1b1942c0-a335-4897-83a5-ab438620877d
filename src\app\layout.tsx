import './globals.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import dynamic from 'next/dynamic'

const CookieBanner = dynamic(() => import('../components/CookieBanner'), { ssr: false })

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Labrador retriever info – Allt om Labrador retrievers',
  description: 'Labrador retriever info – Sveriges bästa informationssida om Labrador retrievers. Fakta, artiklar, blogg och tips om världens mest älskade hundras.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="sv">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/images/logo.png" />
        <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;700&display=swap" rel="stylesheet" />
        {/* Google Analytics placeholder */}
      </head>
      <body className={inter.className + ' bg-white text-gray-900 font-sans'}>
        <CookieBanner />
        {children}
      </body>
    </html>
  )
}
