#!/usr/bin/env node

/**
 * Manual sitemap generation script
 * This can be used for testing or manual sitemap generation
 */

const fs = require('fs')
const path = require('path')
const matter = require('gray-matter')

// Inline sitemap utilities for Node.js compatibility
function getStaticPages() {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://labradorretrieverinfo.se'

  const staticPages = [
    { path: '', priority: 1.0, changeFrequency: 'weekly' },
    { path: '/blog', priority: 0.9, changeFrequency: 'daily' },
    { path: '/guider', priority: 0.9, changeFrequency: 'weekly' },
    { path: '/kontakt', priority: 0.7, changeFrequency: 'monthly' },
    { path: '/gdpr/integritetspolicy', priority: 0.3, changeFrequency: 'yearly' },
    { path: '/gdpr/cookiepolicy', priority: 0.3, changeFrequency: 'yearly' },
  ]

  return staticPages.map(page => ({
    url: `${baseUrl}${page.path}`,
    lastModified: new Date(),
    changeFrequency: page.changeFrequency,
    priority: page.priority,
  }))
}

function getBlogPosts() {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://labradorretrieverinfo.se'
  const blogDir = path.join(process.cwd(), 'content/blog')

  let files = []
  try {
    files = fs.readdirSync(blogDir)
  } catch {
    return []
  }

  return files
    .filter(file => file.endsWith('.md'))
    .map(file => {
      const filePath = path.join(blogDir, file)
      const slug = file.replace(/\.md$/, '')
      const stats = fs.statSync(filePath)

      try {
        const content = fs.readFileSync(filePath, 'utf-8')
        const { data } = matter(content)
        const lastModified = data.date ? new Date(data.date) : stats.mtime

        return {
          url: `${baseUrl}/blog/${slug}`,
          lastModified,
          changeFrequency: 'monthly',
          priority: 0.8,
        }
      } catch {
        return {
          url: `${baseUrl}/blog/${slug}`,
          lastModified: stats.mtime,
          changeFrequency: 'monthly',
          priority: 0.8,
        }
      }
    })
    .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
}

function getGuides() {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://labradorretrieverinfo.se'
  const guidesDir = path.join(process.cwd(), 'content/guides')

  let files = []
  try {
    files = fs.readdirSync(guidesDir)
  } catch {
    return []
  }

  return files
    .filter(file => file.endsWith('.md'))
    .map(file => {
      const filePath = path.join(guidesDir, file)
      const slug = file.replace(/\.md$/, '')
      const stats = fs.statSync(filePath)

      try {
        const content = fs.readFileSync(filePath, 'utf-8')
        const { data } = matter(content)
        const lastModified = data.date ? new Date(data.date) : stats.mtime

        return {
          url: `${baseUrl}/guider/${slug}`,
          lastModified,
          changeFrequency: 'monthly',
          priority: 0.8,
        }
      } catch {
        return {
          url: `${baseUrl}/guider/${slug}`,
          lastModified: stats.mtime,
          changeFrequency: 'monthly',
          priority: 0.8,
        }
      }
    })
    .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
}

function getAllSitemapUrls() {
  return [
    ...getStaticPages(),
    ...getBlogPosts(),
    ...getGuides(),
  ]
}

function generateSitemapXml(urls) {
  const urlEntries = urls.map(({ url, lastModified, changeFrequency, priority }) => `
  <url>
    <loc>${url}</loc>
    <lastmod>${lastModified.toISOString()}</lastmod>
    <changefreq>${changeFrequency}</changefreq>
    <priority>${priority}</priority>
  </url>`).join('')

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlEntries}
</urlset>`
}

async function generateSitemap() {
  try {
    console.log('🗺️  Generating sitemap...')

    // Get all URLs
    const urls = getAllSitemapUrls()
    console.log(`📄 Found ${urls.length} URLs to include in sitemap`)

    // Generate XML
    const sitemapXml = generateSitemapXml(urls)

    // Write to public directory
    const outputPath = path.join(process.cwd(), 'public', 'sitemap.xml')
    fs.writeFileSync(outputPath, sitemapXml, 'utf-8')

    console.log(`✅ Sitemap generated successfully at: ${outputPath}`)
    console.log('\n📋 URLs included:')
    urls.forEach(({ url, priority }) => {
      console.log(`  • ${url} (priority: ${priority})`)
    })

  } catch (error) {
    console.error('❌ Error generating sitemap:', error)
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  generateSitemap()
}

module.exports = { generateSitemap }
