import HomeLayout from '../../components/HomeLayout'
import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import Image from 'next/image'
import Link from 'next/link'

export default function GuiderPage() {
  const guidesDir = path.join(process.cwd(), 'content/guides')
  let files: string[] = []
  try {
    files = fs.readdirSync(guidesDir)
  } catch {
    files = []
  }
  const guides = files
    .filter((file) => file.endsWith('.md'))
    .map((file) => {
      const filePath = path.join(guidesDir, file)
      const content = fs.readFileSync(filePath, 'utf-8')
      const { data } = matter(content)
      return {
        slug: file.replace(/.md$/, ''),
        title: data.title || 'Guidad artikel',
        date: data.date || '',
        excerpt: data.excerpt || '',
        image: data.image || '/images/hero.jpg',
      }
    })
    .sort((a, b) => (a.date < b.date ? 1 : -1))

  return (
    <HomeLayout>
      <div className="max-w-4xl mx-auto py-12 px-4">
        <h1 className="text-4xl md:text-5xl font-bold mb-4 text-accent text-center">Guider & tips för dig och din labrador</h1>
        <p className="text-lg mb-10 text-center max-w-2xl mx-auto">Oavsett om du precis fått hem din första valp eller vill veta mer om foder, motion och vardagslivet – här hittar du guider som gör livet enklare och roligare för både dig och din hund.</p>
        <div className="grid md:grid-cols-3 gap-8">
          {guides.map((guide) => (
            <article key={guide.slug} className="bg-white/80 rounded-2xl shadow-lg p-4 flex flex-col items-center border border-yellow-100 hover:shadow-xl transition-shadow animate-fade-in">
              <Image src={guide.image} alt={guide.title} width={320} height={200} className="rounded-xl object-cover mb-4 w-full h-40" />
              <h2 className="text-xl font-bold text-accent mb-2 text-center">{guide.title}</h2>
              <p className="text-gray-700 text-center text-sm mb-3">{guide.excerpt}</p>
              <Link href={`/guider/${guide.slug}`} className="text-primary underline font-semibold">Läs guiden</Link>
            </article>
          ))}
        </div>
      </div>
    </HomeLayout>
  )
}
