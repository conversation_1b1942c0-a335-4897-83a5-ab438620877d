{"name": "vfile-location", "version": "4.1.0", "description": "vfile utility to convert between positional (line and column-based) and offset (range-based) locations", "license": "MIT", "keywords": ["vfile", "vfile-util", "util", "utility", "virtual", "file", "location", "point", "position", "offset"], "repository": "vfile/vfile-location", "bugs": "https://github.com/vfile/vfile-location/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/unist": "^2.0.0", "vfile": "^5.0.0"}, "devDependencies": {"@types/node": "^18.0.0", "c8": "^7.0.0", "prettier": "^2.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^4.0.0", "xo": "^0.53.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --check-coverage --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true}}