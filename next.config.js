/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: ['images.unsplash.com', 'placehold.co'],
    unoptimized: false,
  },
  // Enable experimental features for better sitemap handling
  experimental: {
    // Enable static generation optimizations
    optimizePackageImports: ['gray-matter'],
  },
  // Environment variables
  env: {
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'https://labradorretrieverinfo.se',
  },
}

module.exports = nextConfig;
