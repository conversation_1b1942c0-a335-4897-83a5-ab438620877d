"use client";
// import fs from 'fs'
// import path from 'path'
// import matter from 'gray-matter'
import Link from 'next/link'
import { UserGroupIcon, CakeIcon, HeartIcon } from '@heroicons/react/24/solid'
import { FaPaw } from 'react-icons/fa'

interface BlogListProps {
  limit?: number
}

const icons = [
  <FaPaw className="w-6 h-6 text-primary" key="paw" />,
  <UserGroupIcon className="w-6 h-6 text-primary" key="group" />,
  <CakeIcon className="w-6 h-6 text-primary" key="cake" />,
  <HeartIcon className="w-6 h-6 text-primary" key="heart" />,
]

export default function BlogList({ limit }: BlogListProps) {
  // Blogglistan måste göras om till en Client Component och data måste skickas in som prop från servern.
  return null;
}
