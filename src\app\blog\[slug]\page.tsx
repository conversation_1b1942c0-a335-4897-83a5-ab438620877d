import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import { remark } from 'remark'
import html from 'remark-html'
import HomeLayout from '../../../components/HomeLayout'

export async function generateStaticParams() {
  const blogDir = path.join(process.cwd(), 'content/blog')
  let files: string[] = []
  try {
    files = fs.readdirSync(blogDir)
  } catch {
    files = []
  }
  return files.filter(f => f.endsWith('.md')).map(f => ({ slug: f.replace(/.md$/, '') }))
}

export async function generateMetadata({ params }: { params: { slug: string } }) {
  const blogDir = path.join(process.cwd(), 'content/blog')
  const filePath = path.join(blogDir, params.slug + '.md')
  const content = fs.readFileSync(filePath, 'utf-8')
  const { data } = matter(content)
  return {
    title: data.title + ' – Labrador retriever info',
    description: data.excerpt || data.title,
  }
}

export default async function BlogPost({ params }: { params: { slug: string } }) {
  const blogDir = path.join(process.cwd(), 'content/blog')
  const filePath = path.join(blogDir, params.slug + '.md')
  const fileContent = fs.readFileSync(filePath, 'utf-8')
  const { data, content } = matter(fileContent)
  const processedContent = await remark().use(html).process(content)
  const contentHtml = processedContent.toString()

  return (
    <HomeLayout>
      <article className="prose lg:prose-xl mx-auto animate-fade-in">
        <h1 className="text-3xl font-bold text-accent mb-2">{data.title}</h1>
        <p className="text-gray-600 text-sm mb-4">{data.date}</p>
        <img src={data.image || 'https://placehold.co/800x350?text=Labrador+artikel'} alt={data.title} className="rounded shadow mb-6" />
        <div dangerouslySetInnerHTML={{ __html: contentHtml }} />
      </article>
    </HomeLayout>
  )
}
