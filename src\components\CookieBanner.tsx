'use client';

import { useState } from 'react'

export default function CookieBanner() {
  const [visible, setVisible] = useState(true)
  if (!visible) return null
  return (
    <div className="fixed bottom-0 inset-x-0 bg-gray-100 border-t border-gray-300 p-4 z-50 flex flex-col md:flex-row items-center justify-between animate-fade-in">
      <span className="text-gray-700 text-sm mb-2 md:mb-0">Vi använder cookies för att förbättra din upplevelse. Läs mer i vår <a href='/gdpr/cookiepolicy' className='underline'>cookiepolicy</a>.</span>
      <button
        className="ml-4 px-4 py-2 rounded bg-primary text-accent font-semibold shadow hover:bg-yellow-400 transition-colors"
        onClick={() => setVisible(false)}
        aria-label="Acceptera cookies"
      >
        Acceptera
      </button>
    </div>
  )
}
