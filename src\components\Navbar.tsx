import Link from 'next/link'
import Image from 'next/image'
import { FaPaw } from 'react-icons/fa'

export default function Navbar() {
  return (
    <nav className="bg-white/80 backdrop-blur-lg shadow-xl sticky top-0 z-50 border-b border-yellow-100 animate-fade-in">
      <div className="container mx-auto flex items-center justify-between py-3 md:py-4 px-4 md:px-8">
        <Link href="/" className="flex items-center gap-3 font-extrabold text-2xl tracking-tight hover:opacity-90 transition-opacity text-accent group">
          <span className="relative flex items-center">
            <Image src="/images/logo.png" alt="Labrador retriever info logotyp" width={48} height={48} className="rounded-full shadow-lg border-2 border-yellow-300 group-hover:scale-105 transition-transform duration-200" priority />
            <FaPaw className="absolute -bottom-2 -right-2 w-6 h-6 text-primary drop-shadow-lg animate-bounce-slow" />
          </span>
          <span className="ml-2 bg-gradient-to-r from-yellow-500 via-yellow-400 to-yellow-300 bg-clip-text text-transparent drop-shadow font-nunito">Labrador retriever info</span>
        </Link>
        <div className="flex gap-6 text-lg font-semibold items-center">
          <Link href="/blog" className="relative px-2 py-1 rounded-lg hover:bg-yellow-100 hover:text-primary transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-primary">Blogg</Link>
          <Link href="/guider" className="relative px-2 py-1 rounded-lg hover:bg-yellow-100 hover:text-primary transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-primary">Guider</Link>
          <Link href="/kontakt" className="relative px-2 py-1 rounded-lg hover:bg-yellow-100 hover:text-primary transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-primary">Kontakt</Link>
        </div>
      </div>
    </nav>
  )
}
