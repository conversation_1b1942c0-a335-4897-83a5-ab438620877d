import HomeLayout from '../../components/HomeLayout'
import fs from 'fs'
import path from 'path'
import matter from 'gray-matter'
import Link from 'next/link'
import Image from 'next/image'
import { FaPaw } from 'react-icons/fa'

export const metadata = {
  title: 'Blogg om Labrador retrievers',
  description: '<PERSON><PERSON>s våra senaste blogginlägg och artiklar om Labrador retrievers – tips, fakta och inspiration för dig som älskar Labradors.',
}

export default function BlogPage() {
  const blogDir = path.join(process.cwd(), 'content/blog')
  let files: string[] = []
  try {
    files = fs.readdirSync(blogDir)
  } catch {
    files = []
  }
  const posts = files
    .filter((file) => file.endsWith('.md'))
    .map((file, i) => {
      const filePath = path.join(blogDir, file)
      const content = fs.readFileSync(filePath, 'utf-8')
      const { data } = matter(content)
      return {
        slug: file.replace(/.md$/, ''),
        title: data.title || 'Utan titel',
        date: data.date || '',
        excerpt: data.excerpt || '',
        image: data.image || '/images/blog-traning.jpg',
        icon: <FaPaw className="w-5 h-5 text-primary mr-1 inline-block align-text-bottom" key={i} />, // Liten tass-ikon
      }
    })
    .sort((a, b) => (a.date < b.date ? 1 : -1))

  return (
    <HomeLayout>
      <div className="max-w-5xl mx-auto py-12 px-4">
        <h1 className="text-4xl md:text-5xl font-bold mb-8 text-accent text-center flex items-center justify-center gap-2">
          <FaPaw className="w-8 h-8 text-primary animate-bounce-slow" />
          Blogg & Artiklar
        </h1>
        <p className="text-lg mb-10 text-center max-w-2xl mx-auto">Här delar vi med oss av vardagstips, träning, mat, hälsa och inspirerande berättelser från andra labradorägare. Upptäck allt du vill veta – och lite till!</p>
        <div className="grid md:grid-cols-2 gap-10">
          {posts.map((post) => (
            <article key={post.slug} className="bg-white/80 rounded-2xl shadow-lg p-4 flex flex-col md:flex-row gap-4 border border-yellow-100 hover:shadow-xl transition-shadow animate-fade-in">
              <Image src={post.image} alt={post.title} width={320} height={180} className="rounded-xl object-cover w-full md:w-48 h-32" />
              <div className="flex-1 flex flex-col justify-between">
                <div>
                  <h3 className="text-xl font-bold mb-1 text-accent flex items-center">{post.icon}{post.title}</h3>
                  <p className="text-sm text-gray-600 mb-2">{post.date}</p>
                  <p className="text-gray-800 text-base mb-2">{post.excerpt}</p>
                </div>
                <Link href={`/blog/${post.slug}`} className="text-primary underline font-semibold mt-2">Läs mer</Link>
              </div>
            </article>
          ))}
        </div>
      </div>
    </HomeLayout>
  )
}
