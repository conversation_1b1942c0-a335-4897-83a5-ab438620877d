import HomeLayout from '../../components/HomeLayout'

export const metadata = {
  title: 'Kontakt – Labrador retriever info',
  description: 'Kontakta oss om du har frågor om Labrador retrievers, samarbeten eller vill bidra med innehåll till sajten.'
}

export default function KontaktPage() {
  return (
    <HomeLayout>
      <div className="max-w-2xl mx-auto py-12 px-4">
        <h1 className="text-4xl md:text-5xl font-bold mb-6 text-accent text-center">Hör gärna av dig!</h1>
        <p className="text-lg mb-8 text-center">Har du frågor om labradorer, vill dela med dig av din historia, eller har tips på innehåll? Vi älskar att höra från andra labbe-vänner! Fyll i formuläret så återkommer vi så snart vi kan.</p>
        <form className="bg-white/80 rounded-2xl shadow-lg p-8 flex flex-col gap-4 border border-yellow-100 animate-fade-in">
          <label className="font-semibold">Namn
            <input type="text" className="mt-1 block w-full rounded-lg border border-yellow-200 p-2 focus:ring-primary focus:border-primary" required />
          </label>
          <label className="font-semibold">E-post
            <input type="email" className="mt-1 block w-full rounded-lg border border-yellow-200 p-2 focus:ring-primary focus:border-primary" required />
          </label>
          <label className="font-semibold">Meddelande
            <textarea className="mt-1 block w-full rounded-lg border border-yellow-200 p-2 focus:ring-primary focus:border-primary min-h-[120px]" required />
          </label>
          <button type="submit" className="mt-4 bg-primary text-white font-bold py-2 px-6 rounded-lg shadow hover:bg-yellow-500 transition-colors">Skicka</button>
        </form>
      </div>
    </HomeLayout>
  )
}
