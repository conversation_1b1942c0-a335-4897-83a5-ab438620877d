import Link from 'next/link'
import Image from 'next/image'
import { FaInstagram, FaFacebook, FaPaw } from 'react-icons/fa'

export default function Footer() {
  return (
    <footer className="bg-white/90 border-t border-yellow-200 shadow-inner pt-10 pb-6 px-4 mt-20 animate-fade-in">
      <div className="container mx-auto flex flex-col md:flex-row items-center justify-between gap-8">
        <div className="flex items-center gap-3 mb-4 md:mb-0">
          <span className="relative flex items-center">
            <Image src="/images/logo.png" alt="Labrador retriever info logotyp" width={36} height={36} className="rounded-full shadow border border-yellow-300" />
            <FaPaw className="absolute -bottom-2 -right-2 w-4 h-4 text-primary drop-shadow animate-bounce-slow" />
          </span>
          <span className="ml-2 font-extrabold text-lg tracking-tight bg-gradient-to-r from-yellow-600 via-yellow-400 to-yellow-300 bg-clip-text text-transparent font-nunito">Labrador retriever info</span>
        </div>
        <nav className="flex gap-6 text-base font-medium">
          <Link href="/blog" className="hover:text-primary transition-colors">Blogg</Link>
          <Link href="/kontakt" className="hover:text-primary transition-colors">Kontakt</Link>
          <Link href="/gdpr/integritetspolicy" className="hover:text-primary transition-colors">GDPR</Link>
        </nav>
        <div className="flex gap-4 text-yellow-500 text-xl">
          <a href="#" aria-label="Instagram" className="hover:text-primary transition-colors"><FaInstagram /></a>
          <a href="#" aria-label="Facebook" className="hover:text-primary transition-colors"><FaFacebook /></a>
        </div>
      </div>
      <div className="text-center text-gray-500 text-xs mt-6">
        Labrador retriever info – för dig som älskar labradorer.<br />© {new Date().getFullYear()} Alla rättigheter förbehållna.
      </div>
    </footer>
  )
}
