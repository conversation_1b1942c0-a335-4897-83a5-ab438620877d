import { MetadataRoute } from 'next'
import { getAllSitemapUrls } from '../lib/sitemap'

export default function sitemap(): MetadataRoute.Sitemap {
  const urls = getAllSitemapUrls()

  return urls.map(({ url, lastModified, changeFrequency, priority }) => ({
    url,
    lastModified,
    changeFrequency,
    priority,
  }))
}

// Make it dynamic so it updates immediately when content changes
export const dynamic = 'force-dynamic'
